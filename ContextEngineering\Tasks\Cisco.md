
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
# ContextEngineering\Tasks\Cisco.md
# ContextEngineering\TechnicalNotes\journal-technique.md


# Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.
# ContextEngineering\Export\GUIDE-INTERVENTIONS-MANUELLES-CISCO.md

- 


-  Attention, changement brutal entre le coucher de soleil et le début de la nuit. Attention, je vous ai dit plusieurs fois, quand on change de mode, il faut toujours se rappeler la fin du mode précédent pour éviter les changements brutaux. L<PERSON>, c'est le cas. Là, le mode nuit, c'est n'importe quoi. Au début, on a un truc tout clair avec un machin de couleurs qui sort de je ne sais où. Et puis d'un coup la nuit elle tombe. Voilà, comme ça au moins c'est très bien. Non, je vous ai dit plusieurs fois comment il faut faire. Il faut toujours, c'est une règle d'or, garder ça en mémoire. Il faut toujours garder à l'esprit, garder la fin du mode précédent. Ben oui, si vous gardez la fin du mode précédent, quand vous attaquez le mode d'après, et ben il n'y a pas de changement brutaux. Vous n'avez toujours pas compris. Il faut impérativement adopter cette méthode et cette technique pour tous les mods. À chaque fois qu'on arrive en début de mod, n'importe lequel, il faut toujours que ce mod qui commence se rappelle du mod précédent pour éviter les changements brutaux. 


-  
- 

- 

- 


- Attention, rappel, certains dossiers ne doivent pas être commités sur GitHub, comme le context engineering et certains fichiers sensibles, comme par exemple s'il y a des clés, attention, il ne faut pas qu'elles soient visibles.






































































