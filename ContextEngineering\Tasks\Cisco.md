
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
# ContextEngineering\Tasks\Cisco.md
# ContextEngineering\TechnicalNotes\journal-technique.md


# Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.


- Attention, apportez une correction à la fin du mode nuit pour basculer au mode aube. Il y a un arrêt brutal juste un petit peu avant la fin de nuit. Ça s'arrête brutalement puis ça passe directement à l'aube. Et comme je vous ai dit, il faut toujours rester cohérent et progressif. Donc il faut vérifier cette séquence, à la fin de cette séquence, pourquoi ça s'arrête brutalement. Je l'ai vu grâce aux étoiles. Les étoiles disparaissent subitement et puis d'un coup, elles réapparaissent au mode aube. 


-  


-  Vérifiez aussi que quand toutes les transitions sont finies, donc la dernière c'est la nuit, veuillez créer une boucle. Je m'entends parler quand je dis une boucle. En fait vous répétez pour refaire un autre cycle automatiquement depuis l'aube en continuité. C'est-à-dire que ça ne s'arrête jamais. 

- 

- 

- 


- Attention, rappel, certains dossiers ne doivent pas être commités sur GitHub, comme le context engineering et certains fichiers sensibles, comme par exemple s'il y a des clés, attention, il ne faut pas qu'elles soient visibles.






































































